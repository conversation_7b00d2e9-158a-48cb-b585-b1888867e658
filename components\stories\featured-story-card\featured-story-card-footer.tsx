import React from 'react';
import { But<PERSON> } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';

interface FeaturedStoryCardFooterProps {
  onPress?: () => void;
}

export function FeaturedStoryCardFooter({
  onPress,
}: FeaturedStoryCardFooterProps) {
  return (
    <Button
      className="rounded-full px-6 py-2 bg-primary-500 dark:bg-primary-600"
      onPress={onPress}
    >
      <ButtonText className="text-white font-medium">开始阅读</ButtonText>
    </Button>
  );
}
