import React from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/lib/theme/theme-provider';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { M3EButtonFilled } from '@/components/ui/m3e-button/m3e-buttons';

interface StoryDetailErrorProps {
  error: string;
  onRetry: () => void;
}

export function StoryDetailError({ error, onRetry }: StoryDetailErrorProps) {
  const { t } = useTranslation();
  const { themeColors } = useTheme();

  return (
    <Box className="flex-1 justify-center items-center p-6">
      <Text
        size="lg"
        className="text-red-600 dark:text-red-400 text-center mb-6"
      >
        {error}
      </Text>

      <M3EButtonFilled size="medium" icon="refresh" onPress={onRetry}>
        {t('tryAgain', 'Try Again')}
      </M3EButtonFilled>
    </Box>
  );
}
