import { useContext } from 'react';
// Update ThemeProvider and AppTheme import paths
import { ThemeContext } from '@/lib/theme/theme-provider'; 
import { AppTheme } from '@/lib/theme/themes';

export const useAppTheme = (): AppTheme => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context.theme;
}; 