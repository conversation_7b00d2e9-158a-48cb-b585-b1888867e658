import React from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ThemeOptionsGroup } from '../components/theme-options-group';
import { LanguageOptionsGroup } from '../components/language-options-group';
import { signOut as supabaseSignOut } from '@/api/auth';
import { useAuthStore } from '@/lib/store/auth-store';

import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { ScrollView } from '@/components/ui/scroll-view';
import { Button, ButtonText } from '@/components/ui/m3e-button';

export default function SettingsScreen() {
  const { t } = useTranslation(['common', 'settings']);
  const clearAuthStore = useAuthStore((state) => state.signOut);

  const handleLogout = async () => {
    clearAuthStore();
    const { error } = await supabaseSignOut();
    if (error) {
      console.error('Error signing out from Supabase:', error);
      Alert.alert(
        t('logoutErrorTitle', 'Logout Error'),
        error.message ||
          t('logoutErrorGeneric', 'Failed to sign out. Please try again.')
      );
    }
  };

  return (
    <Box className="flex-1 bg-background-50 dark:bg-background-950">
      {/* Content */}
      <ScrollView className="flex-1">
        {/* Theme Settings Section */}
        <Box className="px-4 mt-6">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            {t('themeTitle')}
          </Text>
          <Box className="bg-background-100 dark:bg-background-900 rounded-md overflow-hidden">
            <ThemeOptionsGroup />
          </Box>
        </Box>

        {/* Language Settings Section */}
        <Box className="px-4 mt-6">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            {t('languageTitle')}
          </Text>
          <Box className="bg-background-100 dark:bg-background-900 rounded-md overflow-hidden">
            <LanguageOptionsGroup />
          </Box>
        </Box>

        {/* Account Section */}
        <Box className="px-4 mt-6 mb-8">
          <Text className="text-typography-900 dark:text-typography-50 text-lg font-medium mb-3">
            {t('accountTitle')}
          </Text>
          <Button
            action="negative"
            variant="solid"
            onPress={handleLogout}
            className="w-full bg-error-500 py-3"
          >
            <ButtonText className="font-medium">{t('logOut')}</ButtonText>
          </Button>
        </Box>
      </ScrollView>
    </Box>
  );
}
