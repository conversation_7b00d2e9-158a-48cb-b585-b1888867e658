// 兼容性导出文件 - 将所有Button导入重定向到M3E组件
// 这个文件提供向后兼容性，同时将所有Button使用迁移到M3E设计系统

import React, { forwardRef } from 'react';
import { ActivityIndicator, View } from 'react-native';
import {
  default as M3EButton,
  M3EButtonFilled,
  M3EButtonTonal,
  M3EButtonOutlined,
  M3EButtonText,
  M3EButtonElevated,
  type M3EButtonProps,
  type M3EButtonVariant,
  type M3EButtonSize,
} from './m3e-buttons';

// Gluestack Button API 到 M3E Button 的映射接口
interface GluestackButtonProps {
  variant?: 'solid' | 'outline' | 'link' | 'ghost';
  action?: 'primary' | 'secondary' | 'positive' | 'negative' | 'default';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  children?: React.ReactNode;
  onPress?: () => void;
  disabled?: boolean;
  isDisabled?: boolean; // 兼容Gluestack的isDisabled属性
  className?: string;
}

// 将Gluestack的variant/action映射到M3E的variant
const mapGluestackToM3E = (
  variant: GluestackButtonProps['variant'] = 'solid',
  action: GluestackButtonProps['action'] = 'primary'
): M3EButtonVariant => {
  // 根据variant和action的组合映射到M3E变体
  if (variant === 'solid') {
    if (action === 'primary') return 'filled';
    if (action === 'secondary') return 'tonal';
    return 'filled';
  }
  if (variant === 'outline') return 'outlined';
  if (variant === 'link') return 'text';
  if (variant === 'ghost') return 'text'; // ghost映射到text变体
  return 'filled';
};

// 将Gluestack的size映射到M3E的size
const mapGluestackSizeToM3E = (
  size: GluestackButtonProps['size'] = 'md'
): M3EButtonSize => {
  if (size === 'xs' || size === 'sm') return 'small';
  if (size === 'lg' || size === 'xl') return 'large';
  return 'medium';
};

// 兼容性Button组件 - 将Gluestack API映射到M3E Button
const Button = forwardRef<View, GluestackButtonProps>(
  (
    {
      variant,
      action,
      size,
      children,
      onPress,
      disabled,
      isDisabled,
      className,
      ...props
    },
    ref
  ) => {
    const m3eVariant = mapGluestackToM3E(variant, action);
    const m3eSize = mapGluestackSizeToM3E(size);
    // 兼容isDisabled和disabled属性
    const isButtonDisabled = disabled || isDisabled;

    // 检查children中是否有ButtonIcon，如果有，提取图标信息
    let iconName: string | undefined;
    let textContent: React.ReactNode = children;

    if (React.Children.count(children) === 1) {
      const child = React.Children.only(children);
      if (React.isValidElement(child) && child.type === ButtonIcon) {
        // 如果只有一个ButtonIcon子元素，这是一个纯图标按钮
        // 我们需要从ButtonIcon的props中提取图标信息
        // 但由于M3E Button需要MaterialSymbol名称，这里我们使用一个默认图标
        iconName = 'more_vert'; // 默认图标
        textContent = undefined;
      }
    }

    return (
      <M3EButton
        ref={ref}
        variant={m3eVariant}
        size={m3eSize}
        onPress={onPress}
        disabled={isButtonDisabled}
        icon={iconName}
        {...props}
      >
        {textContent}
      </M3EButton>
    );
  }
);

// ButtonText组件 - 在M3E Button中，文本直接作为children传递
const ButtonText = forwardRef<
  View,
  { children?: React.ReactNode; className?: string }
>(({ children, className, ...props }, ref) => {
  // M3E Button中文本直接作为children，所以这里只返回children
  return <>{children}</>;
});

// ButtonSpinner组件 - 映射到M3E Button的loading状态
const ButtonSpinner = forwardRef<View, { className?: string }>(
  ({ className, ...props }, ref) => {
    return <ActivityIndicator size="small" {...props} />;
  }
);

// ButtonIcon组件 - 映射到M3E Button的icon属性
const ButtonIcon = forwardRef<
  View,
  {
    as?: React.ComponentType<any>;
    className?: string;
    children?: React.ReactNode;
  }
>(({ as: AsComponent, className, children, ...props }, ref) => {
  // 在M3E Button中，图标通过icon属性传递
  // 这里返回一个占位符，实际使用时需要在Button组件中设置icon属性
  return null;
});

// ButtonGroup组件 - 简单的容器组件
const ButtonGroup = forwardRef<
  View,
  {
    children?: React.ReactNode;
    className?: string;
    space?: string;
    isAttached?: boolean;
    flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  }
>(
  (
    {
      children,
      className,
      space,
      isAttached,
      flexDirection = 'column',
      ...props
    },
    ref
  ) => {
    return (
      <View
        ref={ref}
        style={{
          flexDirection: flexDirection,
          gap: isAttached ? 0 : 8,
        }}
        {...props}
      >
        {children}
      </View>
    );
  }
);

// 设置displayName用于调试
Button.displayName = 'Button';
ButtonText.displayName = 'ButtonText';
ButtonSpinner.displayName = 'ButtonSpinner';
ButtonIcon.displayName = 'ButtonIcon';
ButtonGroup.displayName = 'ButtonGroup';

// 导出兼容性组件（映射到M3E）
export { Button, ButtonText, ButtonSpinner, ButtonIcon, ButtonGroup };

// 导出M3E原生组件
export {
  M3EButtonFilled,
  M3EButtonTonal,
  M3EButtonOutlined,
  M3EButtonText,
  M3EButtonElevated,
  // 向后兼容的 deprecated 导出
  M3Button,
  M3FilledButton,
  M3TonalButton,
  M3OutlinedButton,
  M3TextButton,
  M3ElevatedButton,
} from './m3e-buttons';

// 单独导出M3EButton作为默认导出
export { default as M3EButton } from './m3e-buttons';

// 导出M3E类型
export type {
  M3EButtonProps,
  M3EButtonVariant,
  M3EButtonSize,
  // 向后兼容的 deprecated 类型导出
  M3ButtonProps,
  M3ButtonVariant,
  M3ButtonSize,
} from './m3e-buttons';

// 导出其他M3E组件
export * from './m3e-button-groups';
export * from './m3e-fab';
export * from './m3e-fab-menu';
export * from './m3e-icon-button';
export * from './m3e-split-button';
